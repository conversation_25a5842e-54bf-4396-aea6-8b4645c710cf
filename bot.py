import requests
import json
import base64
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.transaction import Transaction
from solders.message import Message
from solders.instruction import Instruction
from solders.system_program import transfer, TransferParams
import time
from typing import Optional, Dict, Any
import os
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context
import ssl
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TLSAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        kwargs['ssl_context'] = context
        return super(TLSAdapter, self).init_poolmanager(*args, **kwargs)

class SolanaAutoTrader:
    def __init__(self, private_key: str, config_file: str = 'config/trader_config.json', offline_mode: bool = False):
        """
        初始化Solana自动交易器
        :param private_key: Solana私钥（base58格式）
        :param config_file: 配置文件路径
        :param offline_mode: 离线模式，用于测试
        """
        self.keypair = Keypair.from_base58_string(private_key)
        self.public_key = self.keypair.pubkey()
        self.config_file = config_file
        self.config = None
        self.offline_mode = offline_mode
        
        # API端点
        self.valiant_api = "https://testnet-api.valiant.trade"
        # 使用标准的 Solana 测试网端点作为备用
        self.fogo_testnet = "https://testnet.fogo.io/"
        self.solana_testnet = "https://api.testnet.solana.com"
        self.paymaster_api = "https://sessions-example.fogo.io/paymaster"
        
        # 请求头
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3',
            'content-type': 'application/json',
            'dnt': '1',
            'origin': 'https://valiant.trade',
            'priority': 'u=1, i',
            'referer': 'https://valiant.trade/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'solana-client': 'js/2.3.0',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
    
    def load_config(self) -> bool:
        """加载交易配置"""
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.config_file):
                print(f"配置文件不存在: {self.config_file}，使用默认配置")
                self._create_default_config()
                return True

            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print("交易配置加载成功")
            return True
        except Exception as e:
            print(f"配置加载失败: {e}，使用默认配置")
            self._create_default_config()
            return True

    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            "default_swap": {
                "mintA": "So********************************111111112",  # SOL
                "mintB": "fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry",  # fUSD
                "inputAmount": 10000,
                "slippage": 0.01
            }
        }
    
    def get_latest_blockhash(self) -> Optional[str]:
        """获取最新区块哈希"""
        if self.offline_mode:
            # 离线模式返回模拟的区块哈希
            mock_blockhash = "********************************"
            print(f"离线模式: 使用模拟区块哈希: {mock_blockhash}")
            return mock_blockhash

        payload = {
            "id": "4",
            "jsonrpc": "2.0",
            "method": "getLatestBlockhash",
            "params": [{"commitment": "confirmed"}]
        }

        # 尝试多个端点，包括公开的 Solana RPC 端点
        endpoints = [
            self.fogo_testnet,
            "https://api.testnet.solana.com",
            "https://api.devnet.solana.com",
            "https://solana-testnet.rpc.extrnode.com"
        ]

        for endpoint in endpoints:
            print(f"尝试连接端点: {endpoint}")

            # 首先尝试正常的 HTTPS 请求
            try:
                session = requests.Session()
                session.mount('https://', TLSAdapter())
                response = session.post(
                    endpoint,
                    json=payload,
                    headers=self.headers,
                    timeout=15
                )
                if response.status_code == 200:
                    result = response.json()
                    if 'result' in result and 'value' in result['result']:
                        blockhash = result['result']['value']['blockhash']
                        print(f"获取到最新区块哈希: {blockhash}")
                        return blockhash
                else:
                    print(f"获取区块哈希失败: {response.status_code}, {response.text}")
            except Exception as e:
                print(f"端点 {endpoint} 连接异常: {e}")

            # 如果失败，尝试不验证 SSL 证书
            try:
                logger.warning(f"尝试无 SSL 验证连接 {endpoint}")
                response = requests.post(
                    endpoint,
                    json=payload,
                    headers=self.headers,
                    timeout=15,
                    verify=False
                )
                if response.status_code == 200:
                    result = response.json()
                    if 'result' in result and 'value' in result['result']:
                        blockhash = result['result']['value']['blockhash']
                        print(f"获取到最新区块哈希 (无验证): {blockhash}")
                        return blockhash
                else:
                    print(f"无验证获取区块哈希失败: {response.status_code}, {response.text}")
            except Exception as e:
                print(f"无验证端点 {endpoint} 连接异常: {e}")

        print("所有端点均无法连接，启用离线模式")
        self.offline_mode = True
        return self.get_latest_blockhash()  # 递归调用，这次会使用离线模式
    
    def get_swap_transaction(self, mint_a, mint_b, amount, slippage=0.5):
        """获取交换交易数据"""
        endpoints = [
            f"{self.valiant_api}/dex/txs/swap",
            f"{self.valiant_api}/v1/dex/txs/swap"
        ]

        for url in endpoints:
            try:
                headers = self.headers.copy()
                headers.update({'X-Session-Key': '5pMX8rqlmA7vrCLs96TP57pRgykTmW1jxnBPmSz2LXJ'})

                payload = {
                    'userAddress': str(self.public_key),
                    'mintA': mint_a,
                    'mintB': mint_b,
                    'amount': str(amount),
                    'slippage': str(slippage),
                    'sessionKey': '5pMX8rqlmA7vrCLs96TP57pRgykTmW1jxnBPmSz2LXJ',
                    'chainId': 'fogo-testnet'
                }

                print(f"API Request URL: {url}")
                print(f"API Request Payload: {payload}")

                # 首先尝试使用 TLS 适配器
                try:
                    session = requests.Session()
                    session.mount('https://', TLSAdapter())
                    response = session.post(
                        url,
                        json=payload,
                        headers=headers,
                        timeout=15
                    )
                except Exception as tls_error:
                    print(f"TLS 请求失败: {tls_error}，尝试普通请求")
                    # 如果 TLS 适配器失败，尝试普通请求
                    response = requests.post(
                        url,
                        json=payload,
                        headers=headers,
                        timeout=15,
                        verify=False
                    )

                print(f"API Response Status: {response.status_code}")
                print(f"API Response: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    print("成功获取交换交易数据")
                    return result
                print(f"Retrying with next endpoint due to {response.status_code}")

            except Exception as e:
                print(f"端点 {url} 请求异常: {e}")
                continue

        print("所有端点均失败")
        return None
    
    def sign_and_send_transaction(self, transaction_data: str, blockhash: str) -> Optional[str]:
        """签名并发送交易"""
        try:
            print(f"Received transaction data: {transaction_data[:100]}...")
            tx_bytes = base64.b64decode(transaction_data)
            transaction = Transaction.from_bytes(tx_bytes)
            transaction.recent_blockhash = Pubkey.from_string(blockhash)
            transaction.sign([self.keypair])
            signed_tx_b64 = base64.b64encode(bytes(transaction)).decode('utf-8')
            payload = {"transaction": signed_tx_b64}
            print(f"Sending payload: {payload}")

            # 首先尝试使用 TLS 适配器
            try:
                session = requests.Session()
                session.mount('https://', TLSAdapter())
                response = session.post(
                    self.paymaster_api,
                    json=payload,
                    headers=self.headers,
                    timeout=15
                )
            except Exception as tls_error:
                print(f"TLS 请求失败: {tls_error}，尝试普通请求")
                # 如果 TLS 适配器失败，尝试普通请求
                response = requests.post(
                    self.paymaster_api,
                    json=payload,
                    headers=self.headers,
                    timeout=15,
                    verify=False
                )

            print(f"Paymaster Response Status: {response.status_code}")
            print(f"Paymaster Response: {response.text}")

            if response.status_code == 200:
                result = response.json()
                print(f"交易发送成功: {result}")
                return result.get('signature')
            else:
                print(f"交易发送失败: {response.status_code}, {response.text}")
                print("Consider sending original transaction_data without re-signing for testing.")
                return None
        except Exception as e:
            print(f"签名发送交易异常: {e}")
            return None
    
    def execute_swap(self, mint_a: str = None, mint_b: str = None, input_amount: int = None) -> bool:
        """执行交换操作"""
        try:
            if not mint_a:
                mint_a = self.config['default_swap']['mintA']
            if not mint_b:
                mint_b = self.config['default_swap']['mintB']
            if not input_amount:
                input_amount = self.config['default_swap']['inputAmount']
            
            print(f"开始执行交换: {input_amount} {mint_a[:8]}... -> {mint_b[:8]}...")
            
            # 1. 获取最新区块哈希
            blockhash = self.get_latest_blockhash()
            if not blockhash:
                print("无法获取区块哈希，交易终止")
                return False
            
            # 2. 获取交换交易数据
            swap_data = self.get_swap_transaction(mint_a, mint_b, input_amount)
            if not swap_data:
                print("无法获取交换交易数据，交易终止")
                return False
            
            # 3. 提取交易数据
            transaction_data = None
            if isinstance(swap_data, dict):
                possible_keys = ['serializedTx', 'transaction', 'tx', 'serializedTransaction', 'data']
                for key in possible_keys:
                    if key in swap_data:
                        transaction_data = swap_data[key]
                        break
                if not transaction_data:
                    print(f"未找到交易数据，可用键: {list(swap_data.keys())}")
                    return False
            else:
                print(f"意外的数据类型: {type(swap_data)}")
                return False
            
            # 4. 签名并发送交易
            signature = self.sign_and_send_transaction(transaction_data, blockhash)
            if signature:
                print(f"交换成功完成，交易签名: {signature}")
                return True
            else:
                print("交易签名或发送失败")
                return False
        except Exception as e:
            print(f"执行交换异常: {e}")
            return False
    
    def run_auto_trading(self, interval: int = 60):
        """运行自动交易"""
        if not self.load_config():
            return
        
        print(f"开始自动交易，钱包地址: {self.public_key}")
        print(f"交易间隔: {interval}秒")
        
        while True:
            try:
                print("\n=== 开始新的交易周期 ===")
                success = self.execute_swap()
                if success:
                    print("交易成功，等待下一个周期")
                else:
                    print("交易失败，等待重试")
                time.sleep(interval)
            except KeyboardInterrupt:
                print("\n用户中断，停止自动交易")
                break
            except Exception as e:
                print(f"自动交易异常: {e}")
                time.sleep(interval)
    
    def test_connection(self) -> bool:
        """测试各个API连接"""
        print("正在测试API连接...")

        # 测试区块链连接
        print("\n1. 测试区块链RPC连接...")
        blockhash = self.get_latest_blockhash()
        if blockhash:
            print("✅ 区块链RPC连接正常")
            rpc_success = True
        else:
            print("❌ 区块链RPC连接失败")
            rpc_success = False

        # 测试 Valiant API
        print("\n2. 测试 Valiant API 连接...")
        valiant_success = False
        test_endpoints = [
            f"{self.valiant_api}/dex/pools",
            f"{self.valiant_api}/v1/dex/pools",
            f"{self.valiant_api}/health"
        ]

        for test_url in test_endpoints:
            try:
                print(f"尝试连接: {test_url}")
                session = requests.Session()
                session.mount('https://', TLSAdapter())
                response = session.get(test_url, headers=self.headers, timeout=15)
                print(f"响应状态: {response.status_code}")
                if response.status_code in [200, 404]:  # 404 也表示服务器可达
                    print("✅ Valiant API连接正常")
                    valiant_success = True
                    break
                else:
                    print(f"状态码: {response.status_code}, 响应: {response.text[:100]}")
            except Exception as e:
                print(f"连接异常: {e}")
                # 尝试无 SSL 验证
                try:
                    response = requests.get(test_url, headers=self.headers, timeout=15, verify=False)
                    if response.status_code in [200, 404]:
                        print("✅ Valiant API连接正常 (无SSL验证)")
                        valiant_success = True
                        break
                except Exception as e2:
                    print(f"无SSL验证也失败: {e2}")

        if not valiant_success:
            print("❌ Valiant API连接失败")

        # 总结
        print(f"\n=== 连接测试总结 ===")
        print(f"区块链RPC: {'✅' if rpc_success else '❌'}")
        print(f"Valiant API: {'✅' if valiant_success else '❌'}")

        return rpc_success or valiant_success  # 至少一个成功就算通过

    def get_pool_info(self, mint_a: str, mint_b: str) -> Optional[Dict]:
        """获取交易池信息"""
        try:
            pools_url = f"{self.valiant_api}/dex/pools"
            session = requests.Session()
            session.mount('https://', TLSAdapter())
            response = session.get(pools_url, headers=self.headers, timeout=15)
            if response.status_code == 200:
                pools = response.json()
                for pool in pools:
                    if (pool.get('mintA') == mint_a and pool.get('mintB') == mint_b) or \
                       (pool.get('mintA') == mint_b and pool.get('mintB') == mint_a):
                        return pool
                print("未找到匹配的交易池")
                return None
            else:
                print(f"获取池信息失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取池信息异常: {e}")
            return None

if __name__ == "__main__":
    # 建议从环境变量加载私钥，而不是硬编码
    PRIVATE_KEY = "SiMAgop18douUF1Lapk5AUAfe875tQnBATiB8oG1zYkcoWTmg6ZKVtFAkr8jZfCz5YWd793eXSWLzzn2WtTkFkP"
    
    try:
        print("=== Solana自动交易器启动 ===")
        trader = SolanaAutoTrader(PRIVATE_KEY)
        
        expected_address = "6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK"
        if str(trader.public_key) != expected_address:
            print(f"警告：地址不匹配！期望: {expected_address}, 实际: {trader.public_key}")
            exit(1)
        
        print(f"当前钱包地址: {trader.public_key}")
        print(f"私钥已加载: {PRIVATE_KEY[:10]}...{PRIVATE_KEY[-10:]}")
        
        if trader.load_config():
            print("配置加载成功")
        else:
            print("配置加载失败，使用默认配置")
        
        print("\n请选择操作:")
        print("1. 执行单次交换")
        print("2. 运行自动交易")
        print("3. 测试连接")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n=== 执行单次交换 ===")
            success = trader.execute_swap()
            if success:
                print("✅ 单次交换执行成功")
            else:
                print("❌ 单次交换执行失败")
        elif choice == "2":
            interval = input("请输入交易间隔（秒，默认60）: ").strip()
            interval = int(interval) if interval else 60
            print(f"\n=== 开始自动交易，间隔{interval}秒 ===")
            trader.run_auto_trading(interval)
        elif choice == "3":
            print("\n=== 测试连接 ===")
            trader.test_connection()
        elif choice == "4":
            print("退出程序")
        else:
            print("无效选择，退出程序")
            
    except Exception as e:
        print(f"程序启动失败: {e}")