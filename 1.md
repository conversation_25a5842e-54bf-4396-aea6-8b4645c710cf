curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getProgramAccounts","jsonrpc":"2.0","params":["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",{"encoding":"jsonParsed","commitment":"confirmed","filters":[{"dataSize":165},{"memcmp":{"offset":32,"bytes":"6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK"}}]}],"id":"db62cc92-7112-460b-8c3d-5efbbe5ccee0"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=So11111111111111111111111111111111111111112&mint%5B%5D=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry&mint%5B%5D=ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND&mint%5B%5D=7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry",{"encoding":"base64","commitment":"confirmed"}],"id":"904e1a26-7923-439d-b87a-fa1d86d3a012"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["So11111111111111111111111111111111111111112",{"encoding":"base64","commitment":"confirmed"}],"id":"f4c91286-72c5-4651-8570-ddd6272d6c29"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=So11111111111111111111111111111111111111112' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt",{"encoding":"base64","commitment":"confirmed"}],"id":"3dd048ca-81d1-4ddf-b0ff-200e67ae0284"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND",{"encoding":"base64","commitment":"confirmed"}],"id":"35d46977-526c-4ee5-a373-bff696a66c0c"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry",{"encoding":"base64","commitment":"confirmed"}],"id":"b45c8f7b-ad21-4664-85a2-596b71cc415f"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["Bg4ZLGJhL61mnB9debQrXDojeRmQPNGLmVePj6dh1GVe",{"encoding":"base64"}],"id":"ec5e857d-0ec0-418e-a807-33cdd84e04cd"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["So11111111111111111111111111111111111111112",{"encoding":"base64","commitment":"confirmed"}],"id":"5ea84989-17a3-43e0-a30c-a7e4990600d6"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["6dM4TqWyWJsbx7obrdLcviBkTafD5E8av61zfU6jq57X",{"encoding":"base64"}],"id":"1d1aa503-ea3e-4bb9-a144-58e686b6cfee"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt",{"encoding":"base64","commitment":"confirmed"}],"id":"aa24b879-e70b-4146-8359-6522dae70c01"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["6vzAcHmzVtR98SfAfMpPHV5MGUdL5ikth1GXU8fPhxCs",{"encoding":"base64"}],"id":"7c247108-8179-425b-acbf-d9a13a8bd79f"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND",{"encoding":"base64","commitment":"confirmed"}],"id":"79a2f291-e8e4-4afe-a986-ff0eef99f278"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["D5UouARYzKfeZ16vicW6Gn7Rfgn6Yr5twGj3eXYjuU1p",{"encoding":"base64"}],"id":"805a2d01-576d-4f15-8172-afa6e15fac12"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/2.3.0' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"0","jsonrpc":"2.0","method":"getLatestBlockhash","params":[{"commitment":"confirmed"}]}' ;
curl 'https://paymaster.fogo.io/api/sponsor_and_send' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"transaction":"AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA17uk4sfAo7Kwk8DUTxte3SvLTO/Chnn8B9q2F5wCVK+Hecqr0EthKQe2KTehxvMsRDBJKugxqzE/PSg047boPgAIABgzVhLAfJHunFgn3B2xWyB3gd0j2caLkfsUcAOiDYORdyCx6ETsCZZnIq6TxiZmX07dswPO5/lZrOWS2LGsFX6j1KBwjF+JNJCEMVrE0BpN151Ga6p7Hw7FPpBcBetwC51WKP1DbY9mXxpUgEDHmKbjvRPT3RYmMsnLXJBwJL8t1wsS2ltPAYHGBGagoopjBQYn3VDb2W6Rd7Jo4mkChA0H71mYul7Z6zPfvJ4aGMwVTxLalKgmKI+2ORabIKepyshxO85zUFFCcMcJFOlbo8MJG3SAKDuvSCPyD8Gze9XVrxAildzQPyDirDwE4puOmJR3f4O2CkLAJlRoShXNeAUOsjJclj04kifG7PRApFI4NgwtaE5na/xCEBI572Nvp+FkDfUbWfJP7vhL5Qo+DjUD/BXB0SSf0imT8ynBEgAAAAOHQB7eSEcstky48Ns1xjJdBIpK6N+TqPRC0oncoPnrOBpJZHvpwX1780uSlW/+txmKzKEJ+//vb4t7RN+OFFAscvjcnf7Q9a06JB9tZ+B9Gtu9rBbO48cqDbGEPbTOnAgYIBgACBhQMFwEBCAYABQYVDBcBAQgGAAMGDwwXAQEIBgAEBhMMFwEBCQCaBAEAMAD//xAA//9wAKoB//9O85zUFFCcMcJFOlbo8MJG3SAKDuvSCPyD8Gze9XVrxGYTy3jYq1SlOD7sVEKPRPYjFaIaR5T1r0mrbjDcSnEMKuuHnn38IKcM/HeqhfOVJEleKOFU/PdnS8ZNN25HCABGb2dvIFNlc3Npb25zOgpTaWduaW5nIHRoaXMgaW50ZW50IHdpbGwgYWxsb3cgdGhpcyBhcHAgdG8gaW50ZXJhY3Qgd2l0aCB5b3VyIG9uLWNoYWluIGJhbGFuY2VzLiBQbGVhc2UgbWFrZSBzdXJlIHlvdSB0cnVzdCB0aGlzIGFwcCBhbmQgdGhlIGRvbWFpbiBpbiB0aGUgbWVzc2FnZSBtYXRjaGVzIHRoZSBkb21haW4gb2YgdGhlIGN1cnJlbnQgd2ViIGFwcGxpY2F0aW9uLgoKdmVyc2lvbjogMC4xCmNoYWluX2lkOiBmb2dvLXRlc3RuZXQKZG9tYWluOiBodHRwczovL3ZhbGlhbnQudHJhZGUKZXhwaXJlczogMjAyNS0wOC0xNlQwODoyNTo1OC4yODlaCnNlc3Npb25fa2V5OiAzemN0d3hMdVdEWDFmNWZnNFJ0N0dnZTJtU01wREhlRWZpckRENUI4bWsyWQp0b2tlbnM6Ci1mVVNEOiAxNTAwCi1GT0dPOiAxLjUKLVVTRFQ6IDE1MDAKLVVTREM6IDE1MDALFAAKARYQBxcMAhQRBRUNAw8OBBMSAQABy8zRKdqeHouI+8IPNoce1gVQgvKKugquyyRsKDab1OIADAINCwcECgwIBgkAAQ=="}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry",{"encoding":"base64","commitment":"confirmed"}],"id":"ff9620aa-3f4d-4395-8d8b-4db647794da7"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["Bg4ZLGJhL61mnB9debQrXDojeRmQPNGLmVePj6dh1GVe",{"encoding":"base64"}],"id":"fd75aca4-7514-4b3b-b43f-74a3e66a5d0c"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["So11111111111111111111111111111111111111112",{"encoding":"base64","commitment":"confirmed"}],"id":"02a9bde2-3142-463d-ae1e-ec51f692f6e3"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["6dM4TqWyWJsbx7obrdLcviBkTafD5E8av61zfU6jq57X",{"encoding":"base64"}],"id":"cadff306-89a0-4765-8700-74b02f54a265"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt",{"encoding":"base64","commitment":"confirmed"}],"id":"558c9e64-dcd8-4ce2-86e0-2bb7eafc2340"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["6vzAcHmzVtR98SfAfMpPHV5MGUdL5ikth1GXU8fPhxCs",{"encoding":"base64"}],"id":"3b1692c1-86b0-497e-811c-8d97c84f6617"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND",{"encoding":"base64","commitment":"confirmed"}],"id":"d841a895-716b-42d9-9727-db28f5045a74"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["D5UouARYzKfeZ16vicW6Gn7Rfgn6Yr5twGj3eXYjuU1p",{"encoding":"base64"}],"id":"3613da7c-6f56-4a69-acdf-32ae89a0a514"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/2.3.0' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"1","jsonrpc":"2.0","method":"getLatestBlockhash","params":[{"commitment":"confirmed"}]}' ;
curl 'https://paymaster.fogo.io/api/sponsor_and_send' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"transaction":"AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACLgeAxIzPTZiHLSqynJ2qGPvU1MNrdl4yiVQq9RfAt/phDMfLmKApjCnaPoOOAwMdBJCVVGJ+a0S14xGKhvtENgAIABgzVhLAfJHunFgn3B2xWyB3gd0j2caLkfsUcAOiDYORdyMxBwDHNn3SQBw/D74NmpVS4fIF7pvCkCD/Fyi8ePd2pKBwjF+JNJCEMVrE0BpN151Ga6p7Hw7FPpBcBetwC51WKP1DbY9mXxpUgEDHmKbjvRPT3RYmMsnLXJBwJL8t1wsS2ltPAYHGBGagoopjBQYn3VDb2W6Rd7Jo4mkChA0H71mYul7Z6zPfvJ4aGMwVTxLalKgmKI+2ORabIKepyshxO85zUFFCcMcJFOlbo8MJG3SAKDuvSCPyD8Gze9XVrxAildzQPyDirDwE4puOmJR3f4O2CkLAJlRoShXNeAUOsjJclj04kifG7PRApFI4NgwtaE5na/xCEBI572Nvp+FkDfUbWfJP7vhL5Qo+DjUD/BXB0SSf0imT8ynBEgAAAAOHQB7eSEcstky48Ns1xjJdBIpK6N+TqPRC0oncoPnrOBpJZHvpwX1780uSlW/+txmKzKEJ+//vb4t7RN+OFFAvPOzSe5wrz6g3xSvbLf4vYd4WhdzvyrI6hfmMUpqUKVgYIBgACBhQMFwEBCAYABQYVDBcBAQgGAAMGDwwXAQEIBgAEBhMMFwEBCQCaBAEAMAD//xAA//9wAKoB//9O85zUFFCcMcJFOlbo8MJG3SAKDuvSCPyD8Gze9XVrxDrjdPn7F9sGUu/yELdQMoi0cMQkax3ZL3Aj9ibpZ1yJE5yH+gLfxsq7GNch/5Cb+BTIAuhMo4saoHq62t4wfAxGb2dvIFNlc3Npb25zOgpTaWduaW5nIHRoaXMgaW50ZW50IHdpbGwgYWxsb3cgdGhpcyBhcHAgdG8gaW50ZXJhY3Qgd2l0aCB5b3VyIG9uLWNoYWluIGJhbGFuY2VzLiBQbGVhc2UgbWFrZSBzdXJlIHlvdSB0cnVzdCB0aGlzIGFwcCBhbmQgdGhlIGRvbWFpbiBpbiB0aGUgbWVzc2FnZSBtYXRjaGVzIHRoZSBkb21haW4gb2YgdGhlIGN1cnJlbnQgd2ViIGFwcGxpY2F0aW9uLgoKdmVyc2lvbjogMC4xCmNoYWluX2lkOiBmb2dvLXRlc3RuZXQKZG9tYWluOiBodHRwczovL3ZhbGlhbnQudHJhZGUKZXhwaXJlczogMjAyNS0wOC0xNlQwODoyNjowNS42MzNaCnNlc3Npb25fa2V5OiBFa0xLYmZRaUdEanJvbnJXdGRoWm0zM2pncDdNM3pqb2FmWlFjSDZ0ajFCNgp0b2tlbnM6Ci1mVVNEOiAxNTAwCi1GT0dPOiAxLjUKLVVTRFQ6IDE1MDAKLVVTREM6IDE1MDALFAAKARYQBxcMAhQRBRUNAw8OBBMSAQABy8zRKdqeHouI+8IPNoce1gVQgvKKugquyyRsKDab1OIADAINCwcECgwIBgkAAQ=="}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/2.3.0' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"2","jsonrpc":"2.0","method":"getSlot","params":[{"commitment":"confirmed"}]}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getBlockHeight","jsonrpc":"2.0","params":[{"commitment":"confirmed"}],"id":"c23d1802-4b91-4f46-8f6e-28a8b89fa88f"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getSignatureStatuses","jsonrpc":"2.0","params":[["2Lb2DAVdX7ZCyU6Rv72ZSv8Cy6q6To9U4cVqPv4gkuRw9iMm1oQxuf3Yr9was2WNAbYWgTuhUjDeknH8zbY4qLZD"]],"id":"04ee80c0-9d50-4c68-a11d-f2865d973427"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["EkLKbfQiGDjronrWtdhZm33jgp7M3zjoafZQcH6tj1B6",{"encoding":"base64","commitment":"confirmed"}],"id":"ce65e453-0147-4c76-97f9-0aca3ea4410a"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getProgramAccounts","jsonrpc":"2.0","params":["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",{"encoding":"jsonParsed","commitment":"confirmed","filters":[{"dataSize":165},{"memcmp":{"offset":32,"bytes":"6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK"}}]}],"id":"29f3489b-4935-4e02-880d-fc360d776f05"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry&mint%5B%5D=7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt&mint%5B%5D=ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND&mint%5B%5D=So11111111111111111111111111111111111111112' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getProgramAccounts","jsonrpc":"2.0","params":["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",{"encoding":"jsonParsed","commitment":"confirmed","filters":[{"dataSize":165},{"memcmp":{"offset":32,"bytes":"6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK"}}]}],"id":"9820cd4d-cfd4-4cb1-b13f-73a5ab20077a"}' ;
curl 'https://www.fogo.io/api/token-metadata?mint%5B%5D=7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt&mint%5B%5D=So11111111111111111111111111111111111111112&mint%5B%5D=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry&mint%5B%5D=ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://api.valiant.trade/dex/quote?mintA=So11111111111111111111111111111111111111112&mintB=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry&aForB=false&isExactIn=true&feePayer=FNVBzcjbqJYk7fSjpDrDBUPq5MnBstRM57VDP5Gt8mks&inputAmount=100000' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'if-none-match: W/"c7-02OVuvVKHeIPATxhaj6o+DNvhTk"' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://api.valiant.trade/dex/txs/swap?userAddress=6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK&mintA=So11111111111111111111111111111111111111112&mintB=fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry&aForB=false&isExactIn=true&inputAmount=100000&outputAmount=15736824&poolAddress=3icrkGov4SMod4Ps8UySRwHciNFCHvwiEn1qFJcn7dyH&feePayer=FNVBzcjbqJYk7fSjpDrDBUPq5MnBstRM57VDP5Gt8mks&sessionAddress=EkLKbfQiGDjronrWtdhZm33jgp7M3zjoafZQcH6tj1B6' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/2.3.0' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"3","jsonrpc":"2.0","method":"getLatestBlockhash","params":[{"commitment":"confirmed"}]}' ;
curl 'https://paymaster.fogo.io/api/sponsor_and_send' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"transaction":"AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACmZfRRPViljqd8nmbKz0p5aOp6T1mIFRPnkmgz7p9bak/+/z6SA2V8nAj8poOVcaccLVEeXiFPxvNG1ec2lM8EgAIBCRPVhLAfJHunFgn3B2xWyB3gd0j2caLkfsUcAOiDYORdyMxBwDHNn3SQBw/D74NmpVS4fIF7pvCkCD/Fyi8ePd2pKBwjF+JNJCEMVrE0BpN151Ga6p7Hw7FPpBcBetwC51UoYLs//z2ss9GEHByd1zJuyktnRxTm2HZcXL2T07L8sFZGQ9PWDb97m7+HLIF1hBRnhJ1gvy9/+YAwU2Js6wNJXfSislov+73dLbxZ1oT1x4zgXzwhknuNowndoHYHvZ6Dpk6gSf9NxHBcV6E2R+ca5aIJDAgDOLF8Vi4Kgsptyp3eX9FgAT75PK8Uy7GeLlgG/rY2BxmOkvMacwhhEvnGvbzJfB1xUAZ7bzq+f7ozYTEoZPAoSIiyS54TlrvKIdzWZi6XtnrM9+8nhoYzBVPEtqUqCYoj7Y5Fpsgp6nKyHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIn2KT6Cd46SraqHyiuVteRaOBDtO4LgaSbq82SS3wXlO85zUFFCcMcJFOlbo8MJG3SAKDuvSCPyD8Gze9XVrxH3cX12oOOf00gyH2cdWPuuBAq+lYThMy6V1qboZVdWvjJclj04kifG7PRApFI4NgwtaE5na/xCEBI572Nvp+FkJ2xddiy3KcyEuJGyI7OeuIFZxh/ESvMNXJKbsEBYwkgabiFf+q4GE+2h/Y0YYwDXaxDncGus7VZig8AAAAAABBt324ddloZPZy+FGzut5rBy0he1fWzeROoz1hX7/AKkNx6i5w+1XbxqLPYy6OOY3T/950zxLMdBrWrSMps8DrzSNgiBmUdag/lNGDDqCemJogiS0/ncw0CLrl3lXtw/aAw4GAAIMDwoRAQEOBgAJDBAKEQEBEgwRAQMJBQIIBgQHDQsq+MaekeF1h8ighgEAAAAAAPgf8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAA=="}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json; charset=utf-8' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/2.3.0' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"4","jsonrpc":"2.0","method":"getSlot","params":[{"commitment":"confirmed"}]}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getBlockHeight","jsonrpc":"2.0","params":[{"commitment":"confirmed"}],"id":"8a8e52b6-d55c-4d1e-95e0-ddec0f12387a"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getSignatureStatuses","jsonrpc":"2.0","params":[["3tV1w9X5E96NXbYbHdLHppuGZxj2pcY3u3HMb7FM4biyxdUK5sR8Xsj4SdsYTUCCqvbw3fEKyou9dMBLYttj1SyF"]],"id":"b106cc6f-38f2-4b9b-97cc-2bb912867ed6"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getTransaction","jsonrpc":"2.0","params":["3tV1w9X5E96NXbYbHdLHppuGZxj2pcY3u3HMb7FM4biyxdUK5sR8Xsj4SdsYTUCCqvbw3fEKyou9dMBLYttj1SyF",{"encoding":"jsonParsed","commitment":"confirmed","maxSupportedTransactionVersion":0}],"id":"cf2f395f-13c3-4b4b-acec-538da5b471e1"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getTokenAccountsByOwner","jsonrpc":"2.0","params":["6KCFevkBhab6Vq9yDQp6FcXaJPDTLGEf4dGJ3bvNYSGK",{"programId":"TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"},{"encoding":"jsonParsed","commitment":"confirmed"}],"id":"e9014f8a-d7ce-495b-8db0-4519a679ac20"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["7fc38fbxd1q7gC5WqfauwdVME7ms64VGypyoHaTnLUAt",{"encoding":"base64","commitment":"confirmed"}],"id":"ed34074d-3a2a-4c47-999b-664c9340f409"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["So11111111111111111111111111111111111111112",{"encoding":"base64","commitment":"confirmed"}],"id":"2efcaa01-3938-455d-bb7f-aa20c8c560f3"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["fUSDNGgHkZfwckbr5RLLvRbvqvRcTLdH9hcHJiq4jry",{"encoding":"base64","commitment":"confirmed"}],"id":"c17a353c-a47c-4915-bcea-8088ffb97229"}' ;
curl 'https://testnet.fogo.io/' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ja;q=0.6,fr;q=0.5,ru;q=0.4,und;q=0.3' \
  -H 'content-type: application/json' \
  -H 'dnt: 1' \
  -H 'origin: https://valiant.trade' \
  -H 'priority: u=1, i' \
  -H 'referer: https://valiant.trade/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'solana-client: js/1.0.0-maintenance' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"method":"getAccountInfo","jsonrpc":"2.0","params":["ELNbJ1RtERV2fjtuZjbTscDekWhVzkQ1LjmiPsxp5uND",{"encoding":"base64","commitment":"confirmed"}],"id":"6cddce3c-0d63-4820-ad3b-0a1b1247258c"}'